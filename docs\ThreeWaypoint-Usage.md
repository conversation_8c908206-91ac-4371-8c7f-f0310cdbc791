# Three.js 3D航线功能使用说明

## 概述

本功能实现了完整的3D航线系统，包括：
1. 在地图上点击创建带有经纬度和高度信息的3D航点
2. 多个航点自动连接形成3D航线路径
3. 飞行器沿着3D航线自动飞行动画
4. 实时的3D可视化效果，与高德地图完美集成

## 功能特性

### 3D航点功能
- ✅ 点击地图创建3D航点
- ✅ 航点包含经纬度和高度信息
- ✅ 实时高度调整和3D显示更新
- ✅ 航点拖拽移动位置
- ✅ 航点选中状态管理
- ✅ 撤销/重做操作支持
- ✅ 批量清空航点

### 3D航线功能
- ✅ 多航点自动连接成3D路径
- ✅ 平滑的曲线插值算法
- ✅ 可自定义路径颜色和宽度
- ✅ 路径发光效果
- ✅ 实时路径更新

### 飞行器动画
- ✅ 飞行器沿航线自动飞行
- ✅ 真实的飞行速度控制
- ✅ 自动朝向飞行方向
- ✅ 循环飞行动画
- ✅ 多飞行器支持

### 3D视觉效果
- ✅ 高质量的3D渲染
- ✅ 阴影和光照效果
- ✅ 平滑的动画过渡
- ✅ 与地图完美融合

## 核心组件

### ThreeWaypointManager 类

位置：`src/utils/ThreeWaypointManager.js`

主要方法：
- `addWaypoint(id, longitude, latitude, height, options)` - 添加3D航点
- `removeWaypoint(id)` - 删除3D航点
- `updateWaypointHeight(id, newHeight)` - 更新航点高度
- `selectWaypoint(id)` - 选中航点
- `dispose()` - 清理资源

### ThreeFlightPathManager 类

位置：`src/utils/ThreeFlightPathManager.js`

主要方法：
- `createFlightPath(pathId, waypoints, options)` - 创建3D航线
- `removeFlightPath(pathId)` - 删除3D航线
- `createAircraft(aircraftId, pathId, options)` - 创建飞行器
- `updateAnimations()` - 更新动画
- `dispose()` - 清理资源

### 航线规划页面集成

位置：`src/views/flightAirline/flightAirline.vue`

已集成的功能：
- 地图点击创建航点时自动创建对应的3D航点
- 航点高度修改时同步更新3D显示
- 航点删除时同步删除3D航点
- 撤销/重做操作时同步3D航点状态

## 使用方法

### 1. 启用3D航点功能

```javascript
// 在组件data中设置
data() {
  return {
    use3DWaypoints: true, // 启用3D航点
    threeWaypointManager: null
  }
}

// 初始化3D航点管理器
initThreeWaypointManager() {
  try {
    if (this.$refs.amap && this.$refs.amap.map) {
      const map = this.$refs.amap.map
      this.threeWaypointManager = new ThreeWaypointManager(map)
      console.log('3D航点管理器初始化成功')
    }
  } catch (error) {
    console.error('3D航点管理器初始化失败:', error)
  }
}
```

### 2. 创建3D航点

```javascript
// 点击地图创建航点
addWaypoint(lng, lat, height = 100) {
  const wgs84 = this.gcj02ToWgs84(lng, lat)
  
  const waypoint = {
    id: this.generateId(),
    longitude: wgs84.lng,
    latitude: wgs84.lat,
    height: height
  }
  
  // 创建3D航点
  if (this.use3DWaypoints && this.threeWaypointManager) {
    try {
      const result = this.threeWaypointManager.addWaypoint(
        waypoint.id,
        waypoint.longitude,
        waypoint.latitude,
        waypoint.height,
        {
          speed: waypoint.speed,
          actions: waypoint.actions
        }
      )
      console.log('3D航点创建成功:', result)
    } catch (error) {
      console.error('3D航点创建失败:', error)
    }
  }
}
```

### 3. 更新航点高度

```javascript
updateWaypointHeight(waypointId, newHeight) {
  if (this.use3DWaypoints && this.threeWaypointManager) {
    try {
      this.threeWaypointManager.updateWaypointHeight(waypointId, newHeight)
      console.log('3D航点高度更新成功')
    } catch (error) {
      console.error('3D航点高度更新失败:', error)
    }
  }
}
```

### 4. 删除航点

```javascript
deleteWaypoint(waypointId) {
  // 删除3D航点
  if (this.use3DWaypoints && this.threeWaypointManager) {
    try {
      this.threeWaypointManager.removeWaypoint(waypointId)
      console.log('3D航点删除成功')
    } catch (error) {
      console.error('3D航点删除失败:', error)
    }
  }
}
```

## 测试页面

创建了专门的测试页面：`src/views/test/ThreeWaypointTest.vue`

功能包括：
- 启用/禁用3D航点
- 点击地图创建航点
- 实时调整航点高度
- 删除单个航点
- 清空所有航点

## 3D航点样式配置

```javascript
// 在ThreeWaypointManager中的配置
config: {
  sphereRadius: 50,        // 球体半径
  pillarHeight: 200,       // 支柱高度
  pillarRadius: 8,         // 支柱半径
  baseRadius: 30,          // 基座半径
  colors: {
    sphere: 0x52c41a,      // 球体颜色（绿色）
    pillar: 0x389e0d,      // 支柱颜色（深绿）
    base: 0x7cb342,        // 基座颜色（浅绿）
    selected: 0xff4d4f,    // 选中颜色（红色）
    hover: 0xffa940        // 悬停颜色（橙色）
  }
}
```

## 注意事项

1. **地图初始化**：确保地图完全初始化后再创建ThreeWaypointManager
2. **坐标转换**：使用WGS84坐标系存储，GCJ02坐标系显示
3. **资源清理**：组件销毁时调用`dispose()`方法清理Three.js资源
4. **错误处理**：所有3D操作都包含try-catch错误处理
5. **性能优化**：大量航点时考虑LOD（细节层次）优化

## 依赖项

- Three.js (^0.142.0)
- 高德地图 API 2.0
- Vue.js 2.x
- Element UI

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

支持WebGL的现代浏览器均可正常使用。
