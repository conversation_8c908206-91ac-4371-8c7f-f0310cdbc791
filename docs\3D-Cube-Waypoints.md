# 3D正方体航点功能文档

## 概述

基于Three.js官方文档实现的3D正方体航点功能，使用正方体（BoxGeometry）来表示航点，支持不同颜色区分不同类型的航点。

## 功能特性

### 1. 正方体航点渲染
- 使用Three.js的BoxGeometry创建正方体航点
- 支持实时3D渲染和动画效果
- 正方体带有黑色边框线，增强视觉效果
- 支持浮动和旋转动画

### 2. 颜色分类系统
不同类型的航点使用不同颜色的正方体表示：

| 航点类型 | 颜色 | 十六进制值 | 说明 |
|---------|------|-----------|------|
| 起飞点 | 蓝色 | #1890ff | 无人机起飞位置 |
| 普通航点 | 绿色 | #52c41a | 常规飞行航点 |
| 拍照点 | 紫色 | #722ed1 | 包含拍照动作的航点 |
| 录像点 | 红色 | #f5222d | 包含录像动作的航点 |
| 降落点 | 橙色 | #fa8c16 | 无人机降落位置 |
| 选中状态 | 红色 | #ff4d4f | 当前选中的航点 |

### 3. 动画效果
- **浮动动画**：正方体在垂直方向上轻微浮动
- **旋转动画**：正方体绕三个轴随机旋转
- **边框同步**：边框线跟随正方体一起旋转

### 4. 高度显示
- 每个正方体航点顶部显示高度标签
- 支柱连接地面和正方体，直观显示高度
- 地面基座提供视觉参考

## 使用方法

### 1. 启用3D航点模式
在航线规划页面，点击"切换到3D航点"按钮启用3D正方体航点模式。

### 2. 测试功能
点击"测试3D正方体航点"按钮，系统会自动创建4个不同类型的测试航点：
- 蓝色起飞点（高度100m）
- 紫色拍照点（高度150m）
- 红色录像点（高度200m）
- 橙色降落点（高度120m）

### 3. 创建航点
- 在3D模式下点击地图创建航点
- 航点会根据设置的动作自动选择颜色
- 支持拖拽调整位置和高度

## 技术实现

### 核心类：ThreeWaypointManager

#### 主要方法：
```javascript
// 添加正方体航点
addWaypoint(id, longitude, latitude, height, options)

// 更新航点高度
updateWaypointHeight(id, newHeight)

// 更新航点动作（影响颜色）
updateWaypointActions(id, actions)

// 更新航点类型
updateWaypointType(id, type)

// 选中航点
selectWaypoint(id)

// 删除航点
removeWaypoint(id)
```

#### 配置参数：
```javascript
config: {
  cubeSize: 60,           // 正方体边长
  pillarHeight: 200,      // 支柱高度
  pillarRadius: 8,        // 支柱半径
  baseRadius: 30,         // 基座半径
  colors: {               // 颜色配置
    cube: 0x52c41a,       // 默认正方体颜色
    takeoff: 0x1890ff,    // 起飞点颜色
    landing: 0xfa8c16,    // 降落点颜色
    photo: 0x722ed1,      // 拍照点颜色
    video: 0xf5222d,      // 录像点颜色
    selected: 0xff4d4f    // 选中状态颜色
  }
}
```

## 依赖要求

- Three.js：用于3D渲染
- 高德地图GLCustomLayer：提供3D地图支持
- 现代浏览器：支持WebGL

## 注意事项

1. 需要先启用3D地图模式才能正常显示3D航点
2. 3D航点功能需要较好的显卡支持
3. 在移动设备上可能存在性能限制
4. 建议在测试前确保地图已完全加载

## 更新日志

### v1.0.0 (2025-01-19)
- 实现基于Three.js BoxGeometry的正方体航点
- 支持多种颜色分类系统
- 添加浮动和旋转动画效果
- 实现高度标签和支柱显示
- 添加测试功能和用户界面
