# 3D正方体航点使用指南

## 快速开始

### 1. 访问航线规划页面
- 启动项目：`npm run dev`
- 浏览器访问：http://localhost:81
- 导航到：航线规划 → 航点飞行

### 2. 启用3D正方体航点
1. 在地图右侧工具栏找到"切换到3D航点"按钮（眼睛图标）
2. 点击按钮启用3D航点模式
3. 按钮变为激活状态（高亮显示）

### 3. 测试功能
1. 点击"测试3D正方体航点"按钮（星星图标）
2. 系统会自动创建4个不同颜色的测试航点：
   - 🔵 蓝色正方体：起飞点（100m高度）
   - 🟣 紫色正方体：拍照点（150m高度）
   - 🔴 红色正方体：录像点（200m高度）
   - 🟠 橙色正方体：降落点（120m高度）

### 4. 观察效果
- 正方体会有轻微的浮动动画
- 每个正方体都会缓慢旋转
- 正方体顶部显示高度标签
- 支柱连接地面，直观显示高度
- 黑色边框线增强视觉效果

## 颜色含义

| 颜色 | 类型 | 说明 |
|------|------|------|
| 🔵 蓝色 | 起飞点 | 无人机起飞位置 |
| 🟢 绿色 | 普通航点 | 常规飞行路径点 |
| 🟣 紫色 | 拍照点 | 包含拍照动作的航点 |
| 🔴 红色 | 录像点 | 包含录像动作的航点 |
| 🟠 橙色 | 降落点 | 无人机降落位置 |
| ❤️ 红色高亮 | 选中状态 | 当前选中的航点 |

## 操作说明

### 创建航点
1. 确保已启用3D航点模式
2. 在地图上点击要创建航点的位置
3. 系统会自动创建绿色正方体航点
4. 可以通过右侧面板设置航点属性

### 选择航点
- 点击正方体航点进行选择
- 选中的航点会变为红色高亮
- 右侧面板会显示航点详细信息

### 删除航点
- 右键点击要删除的正方体航点
- 或在右侧航点列表中点击删除按钮

### 调整高度
- 按住Ctrl键拖拽航点可调整高度
- 支柱长度会实时更新
- 高度标签会显示新的高度值

## 技术要求

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 硬件要求
- 支持WebGL的显卡
- 建议4GB以上内存
- 移动设备可能存在性能限制

## 故障排除

### 问题1：3D航点不显示
**解决方案：**
1. 确保浏览器支持WebGL
2. 检查是否启用了3D地图模式
3. 等待地图完全加载后再启用3D航点

### 问题2：动画卡顿
**解决方案：**
1. 关闭其他占用GPU的程序
2. 降低浏览器缩放比例
3. 减少同时显示的航点数量

### 问题3：颜色显示异常
**解决方案：**
1. 刷新页面重新加载
2. 检查航点的动作设置
3. 确认航点类型配置正确

## 开发者信息

- 基于Three.js BoxGeometry实现
- 使用高德地图GLCustomLayer
- 支持实时3D渲染和动画
- 遵循Three.js官方文档标准

## 更新记录

**v1.0.0 (2025-01-19)**
- ✅ 实现正方体航点渲染
- ✅ 支持多种颜色分类
- ✅ 添加动画效果
- ✅ 完成测试功能
- ✅ 编写使用文档
