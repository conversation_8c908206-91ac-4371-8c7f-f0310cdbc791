/**
 * 3D正方体航点功能代码示例
 * 基于Three.js官方文档实现
 */

// 1. 导入ThreeWaypointManager
import { ThreeWaypointManager } from '@/utils/ThreeWaypointManager'

// 2. 初始化3D航点管理器
export default {
  data() {
    return {
      threeWaypointManager: null,
      use3DWaypoints: false
    }
  },

  methods: {
    // 初始化3D航点管理器
    initThreeWaypointManager() {
      try {
        if (!this.$refs.amap || !this.$refs.amap.map) {
          console.error('地图未初始化')
          return
        }

        this.threeWaypointManager = new ThreeWaypointManager(this.$refs.amap.map)
        console.log('3D航点管理器初始化成功')
      } catch (error) {
        console.error('3D航点管理器初始化失败:', error)
        this.$message.error('3D航点功能初始化失败')
      }
    },

    // 创建不同类型的正方体航点
    createDifferentTypeWaypoints() {
      if (!this.threeWaypointManager) {
        console.error('3D航点管理器未初始化')
        return
      }

      // 创建起飞点（蓝色正方体）
      const takeoffPoint = this.threeWaypointManager.addWaypoint(
        'takeoff_001',
        116.52, // 经度
        39.79,  // 纬度
        100,    // 高度
        {
          type: 'takeoff',
          actions: []
        }
      )

      // 创建拍照点（紫色正方体）
      const photoPoint = this.threeWaypointManager.addWaypoint(
        'photo_001',
        116.54,
        39.79,
        150,
        {
          type: 'waypoint',
          actions: [{ type: 'takePhoto' }]
        }
      )

      // 创建录像点（红色正方体）
      const videoPoint = this.threeWaypointManager.addWaypoint(
        'video_001',
        116.56,
        39.79,
        200,
        {
          type: 'waypoint',
          actions: [{ type: 'startRecord' }]
        }
      )

      // 创建降落点（橙色正方体）
      const landingPoint = this.threeWaypointManager.addWaypoint(
        'landing_001',
        116.58,
        39.79,
        120,
        {
          type: 'landing',
          actions: []
        }
      )

      console.log('创建了4个不同类型的正方体航点')
    },

    // 动态更新航点属性
    updateWaypointProperties() {
      if (!this.threeWaypointManager) return

      // 更新航点高度
      this.threeWaypointManager.updateWaypointHeight('photo_001', 180)

      // 更新航点动作（会改变颜色）
      this.threeWaypointManager.updateWaypointActions('photo_001', [
        { type: 'takePhoto' },
        { type: 'startRecord' }
      ])

      // 更新航点类型
      this.threeWaypointManager.updateWaypointType('photo_001', 'video')
    },

    // 选中航点
    selectWaypoint(waypointId) {
      if (!this.threeWaypointManager) return

      this.threeWaypointManager.selectWaypoint(waypointId)
      console.log(`选中航点: ${waypointId}`)
    },

    // 获取所有航点信息
    getAllWaypoints() {
      if (!this.threeWaypointManager) return []

      const waypoints = this.threeWaypointManager.getAllWaypoints()
      console.log('所有航点:', waypoints)
      return waypoints
    },

    // 删除航点
    removeWaypoint(waypointId) {
      if (!this.threeWaypointManager) return

      this.threeWaypointManager.removeWaypoint(waypointId)
      console.log(`删除航点: ${waypointId}`)
    },

    // 清理资源
    cleanup() {
      if (this.threeWaypointManager) {
        this.threeWaypointManager.dispose()
        this.threeWaypointManager = null
        console.log('3D航点管理器已清理')
      }
    },

    // 切换3D航点模式
    toggle3DWaypoints() {
      this.use3DWaypoints = !this.use3DWaypoints
      
      if (this.use3DWaypoints) {
        // 启用3D航点
        this.initThreeWaypointManager()
        this.$message.success('3D正方体航点模式已启用')
      } else {
        // 禁用3D航点
        this.cleanup()
        this.$message.info('3D正方体航点模式已禁用')
      }
    },

    // 批量创建航点
    createBatchWaypoints(waypointData) {
      if (!this.threeWaypointManager) {
        console.error('3D航点管理器未初始化')
        return
      }

      waypointData.forEach((data, index) => {
        const waypoint = this.threeWaypointManager.addWaypoint(
          data.id || `waypoint_${index}`,
          data.longitude,
          data.latitude,
          data.height || 100,
          {
            type: data.type || 'waypoint',
            actions: data.actions || [],
            index: index + 1
          }
        )
        console.log(`创建航点 ${index + 1}:`, waypoint)
      })
    }
  },

  // 生命周期钩子
  mounted() {
    // 页面加载完成后初始化
    this.$nextTick(() => {
      if (this.use3DWaypoints) {
        this.initThreeWaypointManager()
      }
    })
  },

  beforeDestroy() {
    // 页面销毁前清理资源
    this.cleanup()
  }
}

// 使用示例数据
const exampleWaypointData = [
  {
    id: 'start',
    longitude: 116.52,
    latitude: 39.79,
    height: 100,
    type: 'takeoff',
    actions: []
  },
  {
    id: 'wp1',
    longitude: 116.53,
    latitude: 39.80,
    height: 150,
    type: 'waypoint',
    actions: [{ type: 'takePhoto' }]
  },
  {
    id: 'wp2',
    longitude: 116.54,
    latitude: 39.81,
    height: 200,
    type: 'waypoint',
    actions: [{ type: 'startRecord' }]
  },
  {
    id: 'end',
    longitude: 116.55,
    latitude: 39.82,
    height: 120,
    type: 'landing',
    actions: []
  }
]

// 导出示例数据
export { exampleWaypointData }
